import{j as e}from"./ui-vendor-WY5xTeZk.js";import{P as s,M as a}from"./phone-DShN8j-I.js";import{c as t}from"./index-BvdbJObb.js";import"./react-vendor-o6ozJo2K.js";import"./utils-vendor--BulIq_u.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r=t("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),x=()=>e.jsx("section",{id:"contact",className:"px-4",style:{padding:"var(--section-padding-lg) var(--space-4)"},children:e.jsx("div",{className:"max-w-5xl mx-auto text-center",children:e.jsxs("div",{className:"suz-card-glass rounded-3xl border border-white/30 animate-fade-in shadow-2xl",style:{padding:"var(--space-16)"},children:[e.jsxs("h2",{className:"suz-text-display-lg text-slate-800 mb-8",children:[e.jsx("span",{className:"gradient-text",children:"Kontakt"})," aufnehmen"]}),e.jsx("p",{className:"suz-text-heading-lg text-slate-600 mb-12",children:"Kontaktieren Sie uns jetzt – schnell & unkompliziert!"}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-8 mb-16",children:[e.jsxs("a",{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"suz-btn-primary bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 px-12 py-6 rounded-full text-xl shadow-2xl flex items-center justify-center gap-3","aria-label":"Kontakt über WhatsApp aufnehmen",children:[e.jsx(s,{className:"w-6 h-6","aria-hidden":"true"}),"WhatsApp"]}),e.jsxs("a",{href:"mailto:<EMAIL>",className:"suz-btn-primary bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-12 py-6 rounded-full text-xl shadow-2xl flex items-center justify-center gap-3","aria-label":"E-Mail an SUZ Reinigung senden",children:[e.jsx(a,{className:"w-6 h-6","aria-hidden":"true"}),"E-Mail"]})]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-8 text-slate-600",children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(s,{className:"w-8 h-8 mb-4",style:{color:"var(--suz-blue-primary)"},"aria-hidden":"true"}),e.jsx("h3",{className:"suz-text-heading-md font-semibold text-slate-800 mb-3",children:"Telefon"}),e.jsx("p",{className:"suz-text-body-lg",children:e.jsx("a",{href:"tel:+*************",className:"hover:text-blue-600 transition-colors",children:"+49 176 23152477"})})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(a,{className:"w-8 h-8 mb-4",style:{color:"var(--suz-blue-primary)"},"aria-hidden":"true"}),e.jsx("h3",{className:"suz-text-heading-md font-semibold text-slate-800 mb-3",children:"E-Mail"}),e.jsx("p",{className:"suz-text-body-lg",children:e.jsx("a",{href:"mailto:<EMAIL>",className:"hover:text-blue-600 transition-colors",children:"<EMAIL>"})})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(r,{className:"w-8 h-8 mb-4",style:{color:"var(--suz-blue-primary)"},"aria-hidden":"true"}),e.jsx("h3",{className:"suz-text-heading-md font-semibold text-slate-800 mb-3",children:"Adresse"}),e.jsxs("address",{className:"suz-text-body-lg not-italic",children:["Paul-Langen-Straße 39",e.jsx("br",{}),"53229 Bonn"]})]})]})]})})});export{x as default};
