import{j as e}from"./ui-vendor-WY5xTeZk.js";import{P as t,M as a}from"./phone-DShN8j-I.js";import{c as s}from"./index-BvdbJObb.js";import"./react-vendor-o6ozJo2K.js";import"./utils-vendor--BulIq_u.js";import"./query-vendor--kaXPEoe.js";import"./router-vendor-CClBJTgV.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l=s("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=s("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r=s("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),h=()=>e.jsx("footer",{className:"py-16 px-4",role:"contentinfo","aria-label":"Website Footer",children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsx("div",{className:"suz-card-glass rounded-3xl border border-white/30 shadow-xl mb-8",children:e.jsxs("div",{className:"p-8 md:p-12",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h2",{className:"suz-text-heading-xl gradient-text-animated mb-4",children:"SUZ Reinigung"}),e.jsx("p",{className:"suz-text-body-lg text-slate-600 max-w-2xl mx-auto",children:"Ihr vertrauensvoller Partner für professionelle Reinigungsdienstleistungen in höchster Qualität und mit persönlichem Service."})]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-8 mb-12",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"suz-text-heading-lg text-slate-700 mb-6 font-semibold",children:"Kontakt"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("a",{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"suz-btn-secondary inline-flex items-center gap-3 w-full justify-center suz-focus-ring","aria-label":"WhatsApp kontaktieren: +49 176 23152477",children:[e.jsx(t,{className:"w-5 h-5 text-green-600"}),e.jsx("span",{children:"WhatsApp"})]}),e.jsxs("a",{href:"mailto:<EMAIL>",className:"suz-btn-secondary inline-flex items-center gap-3 w-full justify-center suz-focus-ring","aria-label":"E-Mail <NAME_EMAIL>",children:[e.jsx(a,{className:"w-5 h-5 text-blue-600"}),e.jsx("span",{children:"E-Mail"})]})]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"suz-text-heading-lg text-slate-700 mb-6 font-semibold",children:"Servicegebiet"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 text-slate-600",children:[e.jsx(n,{className:"w-4 h-4 text-blue-500"}),e.jsx("span",{className:"suz-text-body-lg",children:"Deutschland"})]}),e.jsxs("div",{className:"flex items-center justify-center gap-2 text-slate-600",children:[e.jsx(l,{className:"w-4 h-4 text-green-500"}),e.jsx("span",{className:"suz-text-body-lg",children:"24/7 Verfügbar"})]}),e.jsxs("div",{className:"flex items-center justify-center gap-2 text-slate-600",children:[e.jsx(r,{className:"w-4 h-4 text-yellow-500"}),e.jsx("span",{className:"suz-text-body-lg",children:"Premium Service"})]})]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"suz-text-heading-lg text-slate-700 mb-6 font-semibold",children:"Schnellzugriff"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{type:"button",className:"suz-nav-link block w-full text-center suz-focus-ring",onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),"aria-label":"Zur Startseite scrollen",children:"Startseite"}),e.jsx("button",{type:"button",className:"suz-nav-link block w-full text-center suz-focus-ring",onClick:()=>document.getElementById("services")?.scrollIntoView({behavior:"smooth"}),"aria-label":"Zu den Leistungen scrollen",children:"Unsere Leistungen"}),e.jsx("button",{type:"button",className:"suz-nav-link block w-full text-center suz-focus-ring",onClick:()=>document.getElementById("contact")?.scrollIntoView({behavior:"smooth"}),"aria-label":"Zum Kontaktbereich scrollen",children:"Kontakt aufnehmen"})]})]})]}),e.jsx("div",{className:"text-center mb-8",children:e.jsx("a",{href:"https://www.suzreinigung.de",target:"_blank",rel:"noopener noreferrer",className:"suz-text-heading-lg gradient-text font-semibold hover:gradient-text-animated transition-all duration-300 suz-focus-ring","aria-label":"Website besuchen: www.suzreinigung.de",children:"www.suzreinigung.de"})})]})}),e.jsx("div",{className:"suz-card-glass rounded-2xl border border-white/20 px-6 py-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[e.jsx("p",{className:"suz-text-body-lg text-slate-600 text-center md:text-left",children:"© 2024 SUZ Reinigung. Alle Rechte vorbehalten."}),e.jsxs("div",{className:"flex items-center gap-6 text-sm text-slate-500",children:[e.jsx("button",{type:"button",className:"hover:text-slate-700 transition-colors duration-200 suz-focus-ring","aria-label":"Datenschutz-Informationen",children:"Datenschutz"}),e.jsx("button",{type:"button",className:"hover:text-slate-700 transition-colors duration-200 suz-focus-ring","aria-label":"Impressum anzeigen",children:"Impressum"}),e.jsx("button",{type:"button",className:"hover:text-slate-700 transition-colors duration-200 suz-focus-ring","aria-label":"Allgemeine Geschäftsbedingungen",children:"AGB"})]})]})})]})});export{h as default};
